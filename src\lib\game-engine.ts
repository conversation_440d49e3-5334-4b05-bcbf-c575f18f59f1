
export type RevenueModel = 'Subscription' | 'Transactional' | 'Freemium';
export type FundingRound = 'None' | 'Angel' | 'Seed' | 'Series A';

export interface GameEvent {
  id: string;
  name: string;
  description: string;
  type: 'positive' | 'negative' | 'neutral';
  effect: (state: GameState) => GameState; // Function to apply the event's effect
  chance: number; // Probability of this event occurring (0-1)
}

export interface GameState {
  cash: number;
  monthlyRevenue: number;
  customers: number;
  teamMorale: number;
  productQuality: number;
  marketingSpend: number; // This is the one-time marketing spend for a campaign
  turn: number; // Represents months
  
  // Detailed expenses
  salaries: number;
  rent: number;
  softwareSubscriptions: number;
  recurringMarketingBudget: number; // This will be the recurring marketing spend

  revenueModel: RevenueModel;

  // Funding related states
  founderEquity: number; // Percentage of ownership
  debt: number; // Total outstanding debt
  debtRepayment: number; // Monthly debt repayment
  fundingRound: FundingRound; // Current funding round achieved

  // New metrics for pitfalls and market dynamics
  productMarketFit: number; // 0-100, higher is better
  gameOver: boolean;
  winCondition: string | null; // Reason for winning
  loseCondition: string | null; // Reason for losing

  // Competitor state
  competitorProductQuality: number;
  competitorMarketingSpend: number;
  competitorPrice: number;

  // Technical Debt
  technicalDebt: number; // 0-100, higher is worse

  // Team Dynamics
  employeeCount: number;

  // Current Event
  currentEvent: string | null; // Description of the event that just occurred

  // Founder's Focus
  founderFocusPoints: number;
  allocatedFocus: {
    product: number;
    fundraising: number;
    sales: number;
    teamManagement: number;
  };

  // Customer Acquisition & Retention
  customerChurnRate: number; // Percentage of customers lost per month (e.g., 0.05 for 5%)
}

export const initialGameState: GameState = {
  cash: 100000,
  monthlyRevenue: 0,
  customers: 0,
  teamMorale: 100,
  productQuality: 10,
  marketingSpend: 0,
  turn: 1,

  // Initial detailed expenses
  salaries: 5000, // Example: Founder's salary or initial team
  rent: 1000,
  softwareSubscriptions: 500,
  recurringMarketingBudget: 0, // Initial recurring marketing budget

  revenueModel: 'Subscription',

  // Initial funding related states
  founderEquity: 100,
  debt: 0,
  debtRepayment: 0,
  fundingRound: 'None',

  // Initial pitfalls related states
  productMarketFit: 0,
  gameOver: false,
  winCondition: null,
  loseCondition: null,

  // Initial competitor state
  competitorProductQuality: 8, // Slightly lower than player initially
  competitorMarketingSpend: 1000, // Initial competitor marketing
  competitorPrice: 12, // Slightly higher than player's assumed $10 per customer

  // Initial technical debt
  technicalDebt: 0,

  // Initial team dynamics
  employeeCount: 1, // Starting with the founder

  // No event initially
  currentEvent: null,

  // Founder's Focus
  founderFocusPoints: 3, // 3 points to allocate per turn
  allocatedFocus: {
    product: 0,
    fundraising: 0,
    sales: 0,
    teamManagement: 0,
  },

  // Customer Acquisition & Retention
  customerChurnRate: 0.05, // 5% churn initially
};

// Define game events
const gameEvents: GameEvent[] = [
  {
    id: 'economic_boom',
    name: 'Economic Boom!',
    description: 'The economy is booming! Customer spending is up.',
    type: 'positive',
    effect: (state) => ({
      ...state,
      customers: state.customers + Math.floor(state.customers * 0.1) + 50, // 10% increase + 50 new customers
      teamMorale: Math.min(100, state.teamMorale + 5),
    }),
    chance: 0.05, // 5% chance per turn
  },
  {
    id: 'server_crash',
    name: 'Server Crash!',
    description: 'Your servers crashed, leading to customer dissatisfaction and lost revenue.',
    type: 'negative',
    effect: (state) => ({
      ...state,
      cash: state.cash - 10000, // Cost of fixing and lost revenue
      customers: Math.max(0, state.customers - Math.floor(state.customers * 0.05)), // Lose 5% of customers
      teamMorale: Math.max(0, state.teamMorale - 10),
      productQuality: Math.max(0, state.productQuality - 5),
    }),
    chance: 0.03, // 3% chance per turn
  },
  {
    id: 'competitor_launch',
    name: 'Competitor Launches New Product!',
    description: 'A major competitor just launched a new product, impacting your market share.',
    type: 'negative',
    effect: (state) => ({
      ...state,
      customers: Math.max(0, state.customers - Math.floor(state.customers * 0.1)), // Lose 10% of customers
      competitorProductQuality: state.competitorProductQuality + 5,
      competitorMarketingSpend: state.competitorMarketingSpend + 2000,
    }),
    chance: 0.04, // 4% chance per turn
  },
  {
    id: 'viral_marketing',
    name: 'Viral Marketing Success!',
    description: 'One of your marketing campaigns went viral, bringing in a flood of new users!',
    type: 'positive',
    effect: (state) => ({
      ...state,
      customers: state.customers + Math.floor(state.customers * 0.2) + 200, // 20% increase + 200 new customers
      cash: state.cash + 5000, // Small bonus from increased visibility
      teamMorale: Math.min(100, state.teamMorale + 10),
    }),
    chance: 0.02, // 2% chance per turn
  },
];

export type GameAction = 
  | { type: 'DEVELOP_FEATURE'; payload: { cost: number; qualityIncrease: number; incurTechnicalDebt?: number } }
  | { type: 'MARKETING_CAMPAIGN'; payload: { cost: number; channel: 'Digital Ads' | 'SEO' | 'Content Marketing' | 'Referral Program' } }
  | { type: 'HIRE_EMPLOYEE'; payload: { cost: number; moraleDecrease: number; salary: number; type: 'Developer' | 'Marketer' | 'Support' } }
  | { type: 'FIRE_EMPLOYEE'; payload: { salary: number; moraleDecrease: number } }
  | { type: 'ADVANCE_TURN' }
  | { type: 'SET_RECURRING_MARKETING_BUDGET'; payload: { amount: number } }
  | { type: 'SET_REVENUE_MODEL'; payload: { model: RevenueModel } }
  | { type: 'SEEK_EQUITY_FUNDING'; payload: { amount: number; equityOffered: number; round: FundingRound } }
  | { type: 'SEEK_DEBT_FUNDING'; payload: { amount: number; interestRate: number; termMonths: number } }
  | { type: 'APPLY_FOR_GRANT'; payload: { amount: number; successChance: number } }
  | { type: 'REFACTOR_CODE'; payload: { cost: number; technicalDebtReduction: number } }
  | { type: 'ALLOCATE_FOUNDER_FOCUS'; payload: { area: 'product' | 'fundraising' | 'sales' | 'teamManagement'; points: number } };

export function gameReducer(state: GameState, action: GameAction): GameState {
  if (state.gameOver) return state; 

  switch (action.type) {
    case 'DEVELOP_FEATURE':
      return {
        ...state,
        cash: state.cash - action.payload.cost,
        productQuality: state.productQuality + action.payload.qualityIncrease,
        technicalDebt: state.technicalDebt + (action.payload.incurTechnicalDebt || 0),
        currentEvent: null, 
      };
    case 'MARKETING_CAMPAIGN':
      let acquiredCustomers = 0;
      const campaignCost = action.payload.cost;

      switch (action.payload.channel) {
        case 'Digital Ads':
          acquiredCustomers = Math.floor(campaignCost / 80); 
          break;
        case 'SEO':
          acquiredCustomers = Math.floor(campaignCost / 150); 
          break;
        case 'Content Marketing':
          acquiredCustomers = Math.floor(campaignCost / 120); 
          break;
        case 'Referral Program':
          acquiredCustomers = Math.floor(campaignCost / 50); 
          break;
      }

      return {
        ...state,
        cash: state.cash - campaignCost,
        marketingSpend: campaignCost, 
        customers: state.customers + acquiredCustomers,
        currentEvent: null, 
      };
    case 'HIRE_EMPLOYEE':
      return {
        ...state,
        cash: state.cash - action.payload.cost,
        teamMorale: state.teamMorale - action.payload.moraleDecrease,
        salaries: state.salaries + action.payload.salary,
        employeeCount: state.employeeCount + 1,
        currentEvent: null, 
      };
    case 'FIRE_EMPLOYEE':
        if (state.employeeCount <= 1) return state; 
        return {
            ...state,
            salaries: state.salaries - action.payload.salary,
            teamMorale: Math.max(0, state.teamMorale - action.payload.moraleDecrease), 
            employeeCount: state.employeeCount - 1,
            currentEvent: null, 
        };
    case 'SET_RECURRING_MARKETING_BUDGET':
        return {
            ...state,
            recurringMarketingBudget: action.payload.amount,
            currentEvent: null, 
        };
    case 'SET_REVENUE_MODEL':
        return {
            ...state,
            revenueModel: action.payload.model,
            currentEvent: null, 
        };
    case 'SEEK_EQUITY_FUNDING':
        const newEquity = state.founderEquity - action.payload.equityOffered;
        return {
            ...state,
            cash: state.cash + action.payload.amount,
            founderEquity: newEquity > 0 ? newEquity : 0, 
            fundingRound: action.payload.round,
            currentEvent: null, 
        };
    case 'SEEK_DEBT_FUNDING':
        const monthlyInterestPayment = (action.payload.amount * action.payload.interestRate) / 12;
        const newDebtRepayment = state.debtRepayment + monthlyInterestPayment + (action.payload.amount / action.payload.termMonths);
        return {
            ...state,
            cash: state.cash + action.payload.amount,
            debt: state.debt + action.payload.amount,
            debtRepayment: newDebtRepayment,
            currentEvent: null, 
        };
    case 'APPLY_FOR_GRANT':
        const success = Math.random() * 100 < action.payload.successChance;
        if (success) {
            return {
                ...state,
                cash: state.cash + action.payload.amount,
                currentEvent: null, 
            };
        } else {
            return { ...state, currentEvent: null }; 
        }
    case 'REFACTOR_CODE':
        return {
            ...state,
            cash: state.cash - action.payload.cost,
            technicalDebt: Math.max(0, state.technicalDebt - action.payload.technicalDebtReduction),
            currentEvent: null, 
        };
    case 'ALLOCATE_FOUNDER_FOCUS':
        const { area, points } = action.payload;
        const currentAllocated = state.allocatedFocus[area];
        const totalAllocated = Object.values(state.allocatedFocus).reduce((sum, val) => sum + val, 0);

        if (totalAllocated + points - currentAllocated > state.founderFocusPoints) {
            return state; 
        }

        return {
            ...state,
            allocatedFocus: {
                ...state.allocatedFocus,
                [area]: points,
            },
            currentEvent: null, 
        };
    case 'ADVANCE_TURN':
      let newState = { ...state };
      newState.currentEvent = null; 

      // --- Apply Founder Focus Effects ---
      if (newState.allocatedFocus.product > 0) {
          newState.productQuality = Math.min(100, newState.productQuality + (newState.allocatedFocus.product * 0.5));
          newState.technicalDebt = Math.max(0, newState.technicalDebt - (newState.allocatedFocus.product * 1));
      }
      if (newState.allocatedFocus.fundraising > 0) {
          newState.cash += newState.allocatedFocus.fundraising * 500;
      }
      if (newState.allocatedFocus.sales > 0) {
          const salesAcquiredCustomers = newState.allocatedFocus.sales * 20; 
          newState.customers += salesAcquiredCustomers;
      }
      if (newState.allocatedFocus.teamManagement > 0) {
          newState.teamMorale = Math.min(100, newState.teamMorale + (newState.allocatedFocus.teamManagement * 2));
      }

      // Reset allocated focus for next turn
      newState.allocatedFocus = {
          product: 0,
          fundraising: 0,
          sales: 0,
          teamManagement: 0,
      };

      // --- Competitor AI (Reactive) ---
      if (newState.productQuality > newState.competitorProductQuality + 2) {
          newState.competitorProductQuality = Math.min(100, newState.competitorProductQuality + 1);
      }
      if (newState.recurringMarketingBudget + newState.marketingSpend > newState.competitorMarketingSpend * 1.5) {
          newState.competitorMarketingSpend = newState.competitorMarketingSpend * 1.2; 
      }
      if (newState.customers > 100 && newState.customers > newState.competitorMarketingSpend / 10) {
          newState.competitorPrice = Math.max(5, newState.competitorPrice * 0.95); 
      }

      // --- Team Productivity and Morale Impact ---
      const productivityMultiplier = newState.teamMorale / 100; 

      // --- Customer Acquisition based on Market Share Model (now also influenced by recurring marketing) ---
      const playerAttractiveness = (newState.productQuality * 5) + (newState.marketingSpend / 100) + (newState.recurringMarketingBudget / 50); 
      const competitorAttractiveness = (newState.competitorProductQuality * 5) + (newState.competitorMarketingSpend / 100);

      const totalAttractiveness = playerAttractiveness + competitorAttractiveness;
      let playerMarketShare = 0;
      if (totalAttractiveness > 0) {
          playerMarketShare = playerAttractiveness / totalAttractiveness;
      }

      const potentialNewCustomers = Math.floor(Math.random() * 500) + 100; 
      const acquiredCustomersThisTurn = Math.floor(potentialNewCustomers * playerMarketShare * productivityMultiplier); 
      newState.customers += acquiredCustomersThisTurn;

      // --- Customer Churn ---
      let effectiveChurnRate = newState.customerChurnRate;
      if (newState.productQuality > 80) effectiveChurnRate = Math.max(0.01, effectiveChurnRate - 0.02); 
      if (newState.teamMorale > 80) effectiveChurnRate = Math.max(0.01, effectiveChurnRate - 0.01); 
      if (newState.productQuality < 30) effectiveChurnRate = Math.min(0.10, effectiveChurnRate + 0.02); 
      if (newState.teamMorale < 30) effectiveChurnRate = Math.min(0.10, effectiveChurnRate + 0.01); 

      const churnedCustomers = Math.floor(newState.customers * effectiveChurnRate);
      newState.customers = Math.max(0, newState.customers - churnedCustomers);

      // --- Revenue Calculation ---
      if (newState.revenueModel === 'Subscription') {
        newState.monthlyRevenue = newState.customers * 10; 
      } else if (newState.revenueModel === 'Transactional') {
        newState.monthlyRevenue = newState.customers * 5; 
      } else if (newState.revenueModel === 'Freemium') {
        newState.monthlyRevenue = Math.floor(newState.customers * 0.05) * 20; 
      }

      // --- Technical Debt Impact ---
      newState.productQuality = Math.max(0, newState.productQuality - Math.floor(newState.technicalDebt / 20));
      newState.teamMorale = Math.max(0, newState.teamMorale - Math.floor(newState.technicalDebt / 10));

      let bugImpact = 0;
      if (newState.technicalDebt > 50 && Math.random() < (newState.technicalDebt / 200)) {
          bugImpact = Math.floor(Math.random() * 5000) + 1000; 
          newState.teamMorale = Math.max(0, newState.teamMorale - 10); 
      }

      // --- Employee Churn due to low morale ---
      let employeesLost = 0;
      if (newState.teamMorale < 50 && newState.employeeCount > 1) { 
          const churnChance = (50 - newState.teamMorale) / 10; 
          if (Math.random() * 10 < churnChance) {
              employeesLost = 1; 
              newState.teamMorale = Math.max(0, newState.teamMorale - 5); 
              newState.salaries = Math.max(0, newState.salaries - 3000); 
          }
      }
      newState.employeeCount -= employeesLost;

      // --- Random Events ---
      const randomEventRoll = Math.random();
      let triggeredEvent: GameEvent | null = null;
      for (const event of gameEvents) {
          if (randomEventRoll < event.chance) {
              triggeredEvent = event;
              break; 
          }
      }

      if (triggeredEvent) {
          newState = triggeredEvent.effect(newState);
          newState.currentEvent = triggeredEvent.description;
      }

      // --- Expenses and Cash Flow ---
      const totalMonthlyBurn = newState.salaries + newState.rent + newState.softwareSubscriptions + newState.recurringMarketingBudget + newState.debtRepayment + bugImpact;
      newState.cash += newState.monthlyRevenue - totalMonthlyBurn;
      
      // --- Check Win/Lose Conditions ---
      if (newState.cash < 0) {
          newState.gameOver = true;
          newState.loseCondition = "You ran out of cash! Your startup couldn't survive.";
      } else if (newState.productMarketFit >= 90 && newState.customers >= 10000 && newState.monthlyRevenue >= 50000 && newState.turn >= 24) {
          newState.gameOver = true;
          newState.winCondition = "Congratulations! You've achieved strong Product-Market Fit, significant customer growth, and sustainable revenue!";
      } else if (newState.teamMorale <= 10) {
          newState.gameOver = true;
          newState.loseCondition = "Your team morale is critically low! Your employees have abandoned ship.";
      } else if (newState.founderEquity <= 10 && newState.cash < 10000) { // Low equity and low cash
          newState.gameOver = true;
          newState.loseCondition = "You've lost too much equity and are running low on cash. You no longer control your company.";
      } else if (newState.turn >= 60 && newState.monthlyRevenue < 10000) { // After 5 years, if revenue is still low
          newState.gameOver = true;
          newState.loseCondition = "After 5 years, your revenue is still too low to be sustainable. Your startup has failed to scale.";
      }

      newState.turn += 1;
      newState.marketingSpend = 0; 

      return newState;
    default:
      return state;
  }
}
