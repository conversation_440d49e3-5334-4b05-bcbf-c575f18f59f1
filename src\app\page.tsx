'use client';

import Head from 'next/head';
import { useReducer, useState } from 'react';
import { gameReducer, initialGameState, GameAction } from '../lib/game-engine';

export default function Home() {
  const [gameState, dispatch] = useReducer(gameReducer, initialGameState);
  const [tempFocus, setTempFocus] = useState(initialGameState.allocatedFocus);

  const handleAction = (action: GameAction) => {
    if (gameState.gameOver) return; // Prevent actions if game is over
    dispatch(action);
  };

  const handleFocusChange = (area: 'product' | 'fundraising' | 'sales' | 'teamManagement', value: number) => {
    const newTempFocus = { ...tempFocus, [area]: value };
    const totalAllocated = Object.values(newTempFocus).reduce((sum, val) => sum + val, 0);

    if (totalAllocated <= gameState.founderFocusPoints) {
      setTempFocus(newTempFocus);
      dispatch({ type: 'ALLOCATE_FOUNDER_FOCUS', payload: { area, points: value } });
    }
  };

  return (
    <div className="bg-gray-900 text-white min-h-screen">
      <Head>
        <title>Founder&apos;s Dilemma</title>
        <meta name="description" content="An entrepreneurial simulation game" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="container mx-auto p-4">
        <header className="text-center py-8">
          <h1 className="text-5xl font-bold text-cyan-400">
            Founder&apos;s Dilemma
          </h1>
          <p className="text-xl text-gray-400 mt-2">
            Your Journey to Build a Business Empire Starts Here
          </p>
        </header>

        {gameState.gameOver && (
          <div className="text-center text-red-500 text-4xl font-bold mb-8">
            {gameState.winCondition && <p className="text-green-500">{gameState.winCondition}</p>}
            {gameState.loseCondition && <p className="text-red-500">{gameState.loseCondition}</p>}
          </div>
        )}

        {gameState.currentEvent && (
          <div className="bg-blue-900 text-white p-4 rounded-lg mb-8 text-center">
            <h2 className="text-2xl font-bold mb-2">Event!</h2>
            <p className="text-xl">{gameState.currentEvent}</p>
          </div>
        )}

        <div id="dashboard" className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center mb-8">
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Cash</h2>
            <p className="text-3xl font-bold text-green-400">${gameState.cash.toLocaleString()}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Monthly Revenue</h2>
            <p className="text-3xl font-bold text-green-400">${gameState.monthlyRevenue.toLocaleString()}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Customers</h2>
            <p className="text-3xl font-bold text-blue-400">{gameState.customers}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Team Morale</h2>
            <p className="text-3xl font-bold text-yellow-400">{gameState.teamMorale}%</p>
          </div>
          {/* Detailed expenses display */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Salaries</h2>
            <p className="text-xl font-bold text-red-400">${gameState.salaries.toLocaleString()}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Rent</h2>
            <p className="text-xl font-bold text-red-400">${gameState.rent.toLocaleString()}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Software Subs.</h2>
            <p className="text-xl font-bold text-red-400">${gameState.softwareSubscriptions.toLocaleString()}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Recurring Marketing</h2>
            <p className="text-xl font-bold text-red-400">${gameState.recurringMarketingBudget.toLocaleString()}</p>
          </div>
          {/* Funding related displays */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Founder Equity</h2>
            <p className="text-xl font-bold text-purple-400">{gameState.founderEquity}%</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Debt</h2>
            <p className="text-xl font-bold text-red-400">${gameState.debt.toLocaleString()}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Debt Repayment</h2>
            <p className="text-xl font-bold text-red-400">${gameState.debtRepayment.toLocaleString()}/month</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Funding Round</h2>
            <p className="text-xl font-bold text-orange-400">{gameState.fundingRound}</p>
          </div>
          {/* Product-Market Fit display */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Product-Market Fit</h2>
            <p className="text-xl font-bold text-green-400">{gameState.productMarketFit}%</p>
          </div>
          {/* Competitor displays */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Competitor Quality</h2>
            <p className="text-xl font-bold text-gray-400">{gameState.competitorProductQuality}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Competitor Marketing</h2>
            <p className="text-xl font-bold text-gray-400">${gameState.competitorMarketingSpend.toLocaleString()}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Competitor Price</h2>
            <p className="text-xl font-bold text-gray-400">${gameState.competitorPrice.toLocaleString()}</p>
          </div>
          {/* Technical Debt display */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Technical Debt</h2>
            <p className="text-xl font-bold text-red-400">{gameState.technicalDebt}%</p>
          </div>
          {/* Employee Count display */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Employees</h2>
            <p className="text-xl font-bold text-blue-400">{gameState.employeeCount}</p>
          </div>
          {/* Customer Churn Rate display */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-400">Churn Rate</h2>
            <p className="text-xl font-bold text-red-400">{(gameState.customerChurnRate * 100).toFixed(1)}%</p>
          </div>
        </div>

        <div id="founder-focus-area" className="bg-gray-800 p-6 rounded-lg mb-8">
          <h2 className="text-2xl font-bold mb-4 text-cyan-400">Founder&apos;s Focus (Points Available: {gameState.founderFocusPoints - Object.values(tempFocus).reduce((sum, val) => sum + val, 0)})</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="focusProduct" className="block text-lg font-semibold text-gray-400">Product ({tempFocus.product})</label>
              <input
                type="range"
                id="focusProduct"
                min="0"
                max={gameState.founderFocusPoints}
                value={tempFocus.product}
                onChange={(e) => handleFocusChange('product', parseInt(e.target.value))}
                className="w-full"
                disabled={gameState.gameOver}
              />
            </div>
            <div>
              <label htmlFor="focusFundraising" className="block text-lg font-semibold text-gray-400">Fundraising ({tempFocus.fundraising})</label>
              <input
                type="range"
                id="focusFundraising"
                min="0"
                max={gameState.founderFocusPoints}
                value={tempFocus.fundraising}
                onChange={(e) => handleFocusChange('fundraising', parseInt(e.target.value))}
                className="w-full"
                disabled={gameState.gameOver}
              />
            </div>
            <div>
              <label htmlFor="focusSales" className="block text-lg font-semibold text-gray-400">Sales ({tempFocus.sales})</label>
              <input
                type="range"
                id="focusSales"
                min="0"
                max={gameState.founderFocusPoints}
                value={tempFocus.sales}
                onChange={(e) => handleFocusChange('sales', parseInt(e.target.value))}
                className="w-full"
                disabled={gameState.gameOver}
              />
            </div>
            <div>
              <label htmlFor="focusTeamManagement" className="block text-lg font-semibold text-gray-400">Team Management ({tempFocus.teamManagement})</label>
              <input
                type="range"
                id="focusTeamManagement"
                min="0"
                max={gameState.founderFocusPoints}
                value={tempFocus.teamManagement}
                onChange={(e) => handleFocusChange('teamManagement', parseInt(e.target.value))}
                className="w-full"
                disabled={gameState.gameOver}
              />
            </div>
          </div>
        </div>

        <div id="decision-area" className="bg-gray-800 p-6 rounded-lg mb-8">
          <h2 className="text-2xl font-bold mb-4 text-cyan-400">Make Your Move</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Develop New Feature - with option to incur technical debt */}
            <div onClick={() => handleAction({ type: 'DEVELOP_FEATURE', payload: { cost: 10000, qualityIncrease: 5 } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Develop New Feature (Quality)</h3>
              <p className="text-gray-400">Cost: $10,000</p>
              <p className="text-gray-400">Increases product quality.</p>
            </div>
            <div onClick={() => handleAction({ type: 'DEVELOP_FEATURE', payload: { cost: 5000, qualityIncrease: 3, incurTechnicalDebt: 10 } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Develop New Feature (Fast)</h3>
              <p className="text-gray-400">Cost: $5,000</p>
              <p className="text-gray-400">Increases product quality, incurs 10% technical debt.</p>
            </div>
            {/* Refactor Code */}
            <div onClick={() => handleAction({ type: 'REFACTOR_CODE', payload: { cost: 7000, technicalDebtReduction: 15 } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Refactor Code</h3>
              <p className="text-gray-400">Cost: $7,000</p>
              <p className="text-gray-400">Reduces technical debt by 15%.</p>
            </div>

            {/* Marketing Campaigns - now with channels */}
            <div onClick={() => handleAction({ type: 'MARKETING_CAMPAIGN', payload: { cost: 5000, channel: 'Digital Ads' } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Marketing: Digital Ads</h3>
              <p className="text-gray-400">Cost: $5,000</p>
              <p className="text-gray-400">Fast customer acquisition.</p>
            </div>
            <div onClick={() => handleAction({ type: 'MARKETING_CAMPAIGN', payload: { cost: 3000, channel: 'SEO' } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Marketing: SEO</h3>
              <p className="text-gray-400">Cost: $3,000</p>
              <p className="text-gray-400">Slower, but long-term organic growth.</p>
            </div>
            <div onClick={() => handleAction({ type: 'MARKETING_CAMPAIGN', payload: { cost: 4000, channel: 'Content Marketing' } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Marketing: Content</h3>
              <p className="text-gray-400">Cost: $4,000</p>
              <p className="text-gray-400">Builds authority, attracts customers.</p>
            </div>
            <div onClick={() => handleAction({ type: 'MARKETING_CAMPAIGN', payload: { cost: 2000, channel: 'Referral Program' } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Marketing: Referral</h3>
              <p className="text-gray-400">Cost: $2,000</p>
              <p className="text-gray-400">Leverage existing customers for growth.</p>
            </div>

            {/* Hire Employee - different types */}
            <div onClick={() => handleAction({ type: 'HIRE_EMPLOYEE', payload: { cost: 2000, moraleDecrease: 5, salary: 3000, type: 'Developer' } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Hire Developer</h3>
              <p className="text-gray-400">Cost: $2,000 (signing bonus)</p>
              <p className="text-gray-400">Monthly Salary: $3,000</p>
              <p className="text-gray-400">Slightly lowers morale.</p>
            </div>
            <div onClick={() => handleAction({ type: 'HIRE_EMPLOYEE', payload: { cost: 1500, moraleDecrease: 3, salary: 2500, type: 'Marketer' } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Hire Marketer</h3>
              <p className="text-gray-400">Cost: $1,500 (signing bonus)</p>
              <p className="text-gray-400">Monthly Salary: $2,500</p>
              <p className="text-gray-400">Slightly lowers morale.</p>
            </div>
            <div onClick={() => handleAction({ type: 'HIRE_EMPLOYEE', payload: { cost: 1000, moraleDecrease: 2, salary: 2000, type: 'Support' } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Hire Support Staff</h3>
              <p className="text-gray-400">Cost: $1,000 (signing bonus)</p>
              <p className="text-gray-400">Monthly Salary: $2,000</p>
              <p className="text-gray-400">Slightly lowers morale.</p>
            </div>
            {/* Fire Employee */}
            <div onClick={() => handleAction({ type: 'FIRE_EMPLOYEE', payload: { salary: 2500, moraleDecrease: 10 } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver || gameState.employeeCount <= 1 ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Fire Employee</h3>
              <p className="text-gray-400">Reduces salaries, significantly lowers morale.</p>
            </div>

            {/* Set Recurring Marketing Budget */}
            <div onClick={() => handleAction({ type: 'SET_RECURRING_MARKETING_BUDGET', payload: { amount: 1000 } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Set Recurring Marketing Budget</h3>
              <p className="text-gray-400">Set to $1,000/month</p>
              <p className="text-gray-400">This will be a recurring expense.</p>
            </div>
            {/* Change Revenue Model */}
            <div onClick={() => handleAction({ type: 'SET_REVENUE_MODEL', payload: { model: 'Transactional' } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Change Revenue Model</h3>
              <p className="text-gray-400">Switch to Transactional Model</p>
              <p className="text-gray-400">Current: {gameState.revenueModel}</p>
            </div>
            {/* Funding decisions */}
            <div onClick={() => handleAction({ type: 'SEEK_EQUITY_FUNDING', payload: { amount: 50000, equityOffered: 20, round: 'Angel' } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Seek Angel Funding</h3>
              <p className="text-gray-400">Receive $50,000 for 20% equity.</p>
            </div>
            <div onClick={() => handleAction({ type: 'SEEK_DEBT_FUNDING', payload: { amount: 20000, interestRate: 0.10, termMonths: 24 } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Seek Debt Funding</h3>
              <p className="text-gray-400">Receive $20,000 (10% interest, 24 months).</p>
            </div>
            <div onClick={() => handleAction({ type: 'APPLY_FOR_GRANT', payload: { amount: 10000, successChance: 60 } })} className={`bg-gray-700 p-4 rounded-lg ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-600 cursor-pointer'}`}>
              <h3 className="text-xl font-bold">Apply for Grant</h3>
              <p className="text-gray-400">Attempt to get $10,000 (60% chance).</p>
            </div>
          </div>
        </div>

        <div className="text-center">
            <button onClick={() => handleAction({ type: 'ADVANCE_TURN' })} className={`bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-2 px-4 rounded ${gameState.gameOver ? 'cursor-not-allowed opacity-50' : ''}`}>
                Advance to Month #{gameState.turn + 1}
            </button>
        </div>

      </main>
    </div>
  );
}
