# Founder's Dilemma: Testing Matrix

This document outlines the testing plan for the Founder's Dilemma game. It covers unit tests for the game engine, integration tests for the UI and game logic, and user acceptance testing (UAT) for the overall experience.

## 1. Unit Testing (Game Engine)

**Objective:** To verify that the core game logic in `game-engine.ts` is working correctly in isolation.

| Feature / Action | Test Case | Expected Outcome |
| :--- | :--- | :--- |
| **Initial State** | Initialize the game | `gameState` should match `initialGameState`. |
| **`DEVELOP_FEATURE`** | Dispatch the action with a specific cost and quality increase | `cash` should decrease by the cost. `productQuality` should increase by the quality increase. |
| **`MARKETING_CAMPAIGN`** | Dispatch the action with a specific cost | `cash` should decrease by the cost. `customers` and `monthlyRevenue` should increase based on the formula. |
| **`HIRE_EMPLOYEE`** | Dispatch the action with a specific cost and morale decrease | `cash` should decrease by the cost. `teamMorale` should decrease by the morale decrease. |
| **`ADVANCE_TURN`** | Dispatch the action | `cash` should be updated based on `monthlyRevenue` and `monthlyBurn`. `turn` should increment by 1. |
| **Financial Model** | Run multiple turns with varying revenue | `cash` should accurately reflect profits or losses over time. |
| **Game Over** | `cash` drops to zero or below | The game should enter a "bankrupt" state. |
| **Technical Debt** | Develop features with "Speed" option | Technical debt should increase. |
| **Product-Market Fit** | Increase customer satisfaction and retention | Product-Market Fit score should increase. |
| **Marketing Channels** | Allocate budget to different channels | Customer acquisition and revenue should reflect channel effectiveness. |
| **Funding Rounds** | Attempt to secure funding | Cash should increase, equity should decrease, and investor expectations should be set. |
| **Runway** | Advance turns with varying burn rates | Runway should accurately reflect months until cash runs out. |
| **Team Morale** | Hire employees, neglect team building | Team morale should decrease, impacting productivity and churn. |
| **Founder's Focus** | Allocate focus points to different areas | Metrics in focused areas should improve, while neglected areas might decline. |
| **Random Events** | Trigger various events | Game state should update according to the event's impact (e.g., cash decrease for a bug, customer increase for a feature). |

## 2. Integration Testing (UI & Game Logic)

**Objective:** To ensure that the UI components correctly interact with the game engine and that the game state is accurately reflected in the UI.

| Component / Flow | Test Case | Expected Outcome |
| :--- | :--- | :--- |
| **Dashboard** | Perform any game action | All metrics in the dashboard (Cash, Revenue, Customers, Morale, Technical Debt, PMF, Runway) should update to reflect the new `gameState`. |
| **Decision Cards** | Click on a decision card | The corresponding action should be dispatched to the `gameReducer`. The UI should update accordingly. |
| **Advance Turn Button** | Click the "Advance Month" button | The `ADVANCE_TURN` action should be dispatched. The turn counter and other metrics should update. |
| **Event Modals** | Trigger a random event | A modal window should appear, displaying the event information. The `gameState` should be updated based on the event's impact. |
| **Founder's Focus UI** | Allocate focus points | The UI should clearly show allocated points and prevent over-allocation. |
| **Funding Round UI** | Initiate a funding round | The UI should present funding options, show equity dilution, and update cash upon success. |

## 3. User Acceptance Testing (UAT)

**Objective:** To gather feedback from target users on the overall gameplay experience, including balance, engagement, and educational value.

| Area | Question / Scenario | Feedback to Gather |
| :--- | :--- | :--- |
| **Onboarding** | A new user starts the game for the first time | Is the tutorial clear? Do they understand the basic concepts? |
| **Game Balance** | Play through a full game | Is the game too easy or too hard? Are there any dominant strategies? |
| **Engagement** | Play for an extended period | Is the game fun and engaging? Does it hold the player's interest? |
| **Educational Value** | After playing, ask the user about key concepts | Did the game help them understand concepts like burn rate, CAC, technical debt, product-market fit, or the trade-offs of funding? |
| **Bugs & Glitches** | Ask the user to report any unexpected behavior | Document and prioritize any bugs found. |
| **Realism** | Ask users (especially entrepreneurs) about the realism of the simulation | Does the game accurately reflect real-world entrepreneurial challenges and decisions? |

This testing matrix will be used throughout the development process to ensure a high-quality and robust final product.